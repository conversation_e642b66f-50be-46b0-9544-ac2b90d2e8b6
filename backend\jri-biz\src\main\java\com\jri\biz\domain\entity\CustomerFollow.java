package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jri.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 跟进记录对象 customer_follow - 旧版跟进记录系统
 *
 * @deprecated 推荐使用新的跟进记录系统 {@link com.jri.biz.cus.domain.entity.CusCcFollow}
 * 新系统提供更好的数据结构和业务逻辑
 *
 * <AUTHOR>
 * @date 2023-06-04
 */
@Deprecated
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_follow")
@ApiModel(value = "customerFollow对象", description = "跟进记录 - 已废弃，推荐使用CusCcFollow")
public class CustomerFollow implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 跟进记录表id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 跟进记录
     */
    @Excel(name = "跟进记录")
    private String followUpRecord;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String contactName;

    /**
     * 跟进方式
     */
    @Excel(name = "跟进方式")
    private String mode;

    /**
     * 跟进时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "跟进时间", width = 30, dateFormat = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;

    /**
     * 跟进人
     */
    @Excel(name = "跟进人")
    private String followUpName;

    /**
     * 部门
     */
    @Excel(name = "部门")
    private String dept;

    /**
     * 逻辑删除标志 1表示删除 0表示未删除
     */
    @Excel(name = "逻辑删除标志 1表示删除 0表示未删除")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 客户信息主表id
     */
    @Excel(name = "客户信息主表id")
    private Long ciId;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
