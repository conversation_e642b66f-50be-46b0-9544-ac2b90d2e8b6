package com.jri.biz.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.domain.convert.CustomerContactConvert;
import com.jri.biz.domain.entity.CustomerContact;
import com.jri.biz.domain.request.CustomerChangeRecordForm;
import com.jri.biz.domain.request.CustomerContactForm;
import com.jri.biz.domain.request.CustomerContactQuery;
import com.jri.biz.domain.vo.CustomerContactListVO;
import com.jri.biz.domain.vo.CustomerContactVO;
import com.jri.biz.mapper.CustomerContactMapper;
import com.jri.common.exception.ServiceException;
import com.jri.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 客户联系人 服务实现类 - 旧版联系人管理系统
 * </p>
 *
 * @deprecated 推荐使用新的联系人管理系统 {@link com.jri.biz.cus.service.CusCcContactService}
 * 此服务仅保留基础功能，新功能开发请使用新系统
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Deprecated
@Service
public class CustomerContactService extends ServiceImpl<CustomerContactMapper, CustomerContact> {

//    @Resource
//    private CustomerContactMapper CustomerContactMapper;
//
//    private final CustomerInformationMapper customerInformationMapper;
//
//    public CustomerContactService(CustomerInformationMapper customerInformationMapper){
//        this.customerInformationMapper = customerInformationMapper;
//    }
    @Resource
    private CustomerContactMapper customerContactMapper;

    @Resource
    private CustomerChangeRecordService customerChangeRecordService;

    /**
     * 列表查询
     *
     * @param query 查询参数
     * @return 查询结果
     */
    public IPage<CustomerContactListVO> listPage(CustomerContactQuery query) {
        var page = new Page<CustomerContactListVO>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().listPage(query, page);
    }

    /**
     * 根据id获取详情
     *
     * @param id id
     * @return 结果
     */
    public CustomerContactVO getDetailById(Long id) {
        return getBaseMapper().getDetailById(id);
    }

    public List<CustomerContactVO> getDetailByCiId(Long ciId) {
        return getBaseMapper().getDetailByCiId(ciId);
    }


    /**
     * 保存
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(CustomerContactForm form) {
        CustomerContact customerContact = CustomerContactConvert.INSTANCE.convert(form);
        //修改
        if(form.getId()!=null){
            //判断是否是第一条
            CustomerContactQuery customerContactQuery = new CustomerContactQuery();
            customerContactQuery.setCiId(form.getCiId());
            IPage<CustomerContactListVO> customerContactListVOIPage = listPage(customerContactQuery);
            if(customerContactListVOIPage.getTotal()==1){
                String firstPhone = customerContactListVOIPage.getRecords().get(0).getPhone();
                String firstName = customerContactListVOIPage.getRecords().get(0).getName();
                if(!firstPhone.equals(form.getName())||!firstName.equals(form.getName())){
                    throw new ServiceException("不允许修改初始填写的联系人或手机号");
                }
            }
            customerContact.setUpdateBy(SecurityUtils.getLoginUser().getUser().getNickName());
        }else {
            customerContact.setCreateBy(SecurityUtils.getLoginUser().getUser().getNickName());
        }
        super.saveOrUpdate(customerContact);
        return customerContact.getId();
    }

    /**
     * 修改
     *
     * @param form 表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CustomerContactForm form) {
        // todo 完善新增/更新逻辑
        CustomerContact customerContact = CustomerContactConvert.INSTANCE.convert(form);
        return updateById(customerContact);
    }

    /**
     * 根据id删除
     *
     * @param id 主键id
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        String updateBy= SecurityUtils.getLoginUser().getUser().getNickName();
        customerContactMapper.deleteById(id,updateBy);
        return removeById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean saveBatch(List<CustomerContactForm> form) {
        var ciId = form.get(0).getCiId();
        var i = getBaseMapper().removeByCiId(ciId);
        var list = form.parallelStream().map(CustomerContactConvert.INSTANCE::convert)
                .toList();
        list.forEach(item -> {
            if (ObjectUtil.isEmpty(item.getId())) {
                item.setCreateTime(LocalDateTime.now());
                item.setCreateBy(SecurityUtils.getLoginUser().getUser().getNickName());
            } else {
                item.setUpdateBy(SecurityUtils.getLoginUser().getUser().getNickName());
                item.setUpdateTime(LocalDateTime.now());
            }
        });
        CustomerChangeRecordForm changeRecordForm = new CustomerChangeRecordForm();
        changeRecordForm.setCiId(ciId);
        changeRecordForm.setContent("编辑");
        changeRecordForm.setInfoSection("联系人");
        customerChangeRecordService.add(changeRecordForm);
        boolean flag = super.saveOrUpdateBatch(list);
        customerChangeRecordService.sendChangeMessage(ciId);
        return flag;

    }
}
