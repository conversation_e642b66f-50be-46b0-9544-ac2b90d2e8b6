package com.jri.biz.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jri.biz.domain.entity.CustomerBank;
import com.jri.biz.domain.entity.CustomerFollow;
import com.jri.biz.domain.request.CustomerBankQuery;
import com.jri.biz.domain.request.CustomerFollowQuery;
import com.jri.biz.domain.vo.CustomerBankListVO;
import com.jri.biz.mapper.CustomerBankMapper;
import com.jri.biz.mapper.CustomerFollowMapper;
import com.jri.common.utils.DateUtils;
import com.jri.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 跟进记录Service业务层处理 - 旧版跟进记录系统
 *
 * @deprecated 推荐使用新的跟进记录系统 {@link com.jri.biz.cus.service.CusCcFollowService}
 * 此服务仅保留基础功能，新功能开发请使用新系统
 *
 * <AUTHOR>
 * @date 2023-06-04
 */
@Deprecated
@Service
public class CustomerFollowService extends ServiceImpl<CustomerFollowMapper, CustomerFollow> {

    @Resource
    private CustomerFollowMapper customerFollowMapper;

    /**
     * 查询跟进记录
     *
     * @param id 跟进记录主键
     * @return 跟进记录
     */

    public CustomerFollow selectCustomerFollowById(Long id)
    {
        return customerFollowMapper.selectCustomerFollowById(id);
    }


    /**
     * 新增跟进记录
     *
     * @param customerFollow 跟进记录
     * @return 结果
     */




    /**
     * 删除跟进记录信息
     *
     * @param id 跟进记录主键
     * @return 结果
     */

    public Boolean deleteCustomerFollowById(Long id)
    {
        String updateBy=SecurityUtils.getLoginUser().getUser().getNickName();
        return customerFollowMapper.deleteCustomerFollowById(id,updateBy);
    }

    public IPage<CustomerFollow> listPage(CustomerFollowQuery query) {
        var page = new Page<CustomerFollow>(query.getPageNum(), query.getPageSize());
        return getBaseMapper().selectCustomerFollowList(query, page);
    }

    public Boolean addOrUpdate(CustomerFollow customerFollow) {
        if(customerFollow.getId()!=null){
            customerFollow.setUpdateBy(SecurityUtils.getLoginUser().getUser().getNickName());
        }else {
            customerFollow.setCreateBy(SecurityUtils.getLoginUser().getUser().getNickName());
        }
        return saveOrUpdate(customerFollow);
    }

}
