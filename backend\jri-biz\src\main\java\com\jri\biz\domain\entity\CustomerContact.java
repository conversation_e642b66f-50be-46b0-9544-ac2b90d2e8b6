package com.jri.biz.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 客户联系人 - 旧版联系人管理系统
 * </p>
 *
 * @deprecated 推荐使用新的联系人管理系统 {@link com.jri.biz.cus.domain.entity.CusCcContact}
 * 新系统提供更好的数据结构和业务逻辑
 *
 * <AUTHOR>
 * @since 2023-05-29
 */
@Deprecated
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_contact")
@ApiModel(value = "CustomerContact对象", description = "客户联系人 - 已废弃，推荐使用CusCcContact")
public class CustomerContact implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id",type= IdType.ASSIGN_ID)
    private Long id;

    /**
     * 客户主表id
     */
    private Long ciId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 电话
     */
    private String phone;

    /**
     * 部门
     */
    private String dept;

    /**
     * 职务
     */
    private String post;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 是否决策人
     */
    private Integer isLeader;

    /**
     * 联系人详情
     */
    private String details;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 是否常用联系人0-否1-是
     */
    private Integer isOften;

    /**
     * 性别0-未知 1-男 2-女
     */
    private String sex;

    /**
     * QQ
     */
    private String qq;

    /**
     * 生日
     */
    private String birthday;
}
